# My First AI Project 🤖

Welcome to your first AI application using Microsoft AI Toolkit and Ollama!

## What This Project Does

This is a simple chat application that connects to your local Ollama models and lets you have conversations with AI.

## Your Setup

- ✅ **VS Code Insiders** - Your development environment
- ✅ **Microsoft AI Toolkit** - AI development tools
- ✅ **Ollama** - Local AI model runner
- ✅ **Python 3.9.6** - Programming language
- ✅ **Node.js v23.11.0** - JavaScript runtime

## Available Models

You currently have these AI models installed:
- `llama3.2:1b` (1.3 GB) - Small, fast model ⚡
- `deepseek-r1:8b` (4.9 GB) - Reasoning model 🧠
- `deepseek-r1:latest` (4.7 GB) - Latest reasoning model
- `llama3:latest` (4.7 GB) - General purpose model
- `llama2:latest` (3.8 GB) - Older but reliable model

## How to Run

1. **Start Ollama service:**
   ```bash
   ollama serve
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the chat application:**
   ```bash
   python simple_chat.py
   ```

## Next Steps

- Try different models by editing the `model_name` in the script
- Add more features like conversation history
- Explore AI Toolkit's advanced features
- Build more complex AI applications

## Learning Resources

- [Microsoft AI Toolkit Documentation](https://marketplace.visualstudio.com/items?itemName=ms-windows-ai-studio.windows-ai-studio)
- [Ollama Documentation](https://ollama.ai/)
- [Python AI Development](https://docs.python.org/3/)

Happy coding! 🚀
