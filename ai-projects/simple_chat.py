#!/usr/bin/env python3
"""
Simple AI Chat Application using Ollama
This is your first AI project with Microsoft AI Toolkit!
"""

import requests
import json
import sys

class OllamaChat:
    def __init__(self, model_name="llama3.2:1b"):
        self.model_name = model_name
        self.base_url = "http://localhost:11434"
        
    def check_ollama_running(self):
        """Check if Ollama service is running"""
        try:
            response = requests.get(f"{self.base_url}/api/tags")
            return response.status_code == 200
        except requests.exceptions.ConnectionError:
            return False
    
    def list_models(self):
        """List available models"""
        try:
            response = requests.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                models = response.json()
                return [model['name'] for model in models['models']]
            return []
        except Exception as e:
            print(f"Error listing models: {e}")
            return []
    
    def chat(self, message):
        """Send a message to the AI model"""
        url = f"{self.base_url}/api/generate"
        
        payload = {
            "model": self.model_name,
            "prompt": message,
            "stream": False
        }
        
        try:
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                return response.json()['response']
            else:
                return f"Error: {response.status_code} - {response.text}"
        except Exception as e:
            return f"Error connecting to Ollama: {e}"

def main():
    print("🤖 Welcome to your first AI Chat Application!")
    print("=" * 50)
    
    # Initialize chat
    chat = OllamaChat()
    
    # Check if Ollama is running
    if not chat.check_ollama_running():
        print("❌ Ollama is not running!")
        print("Please start Ollama first by running: ollama serve")
        sys.exit(1)
    
    # List available models
    models = chat.list_models()
    print(f"📦 Available models: {', '.join(models)}")
    print(f"🎯 Using model: {chat.model_name}")
    print("\n💬 Start chatting! (type 'quit' to exit)")
    print("-" * 50)
    
    # Chat loop
    while True:
        try:
            user_input = input("\nYou: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if not user_input:
                continue
            
            print("🤖 AI: ", end="", flush=True)
            response = chat.chat(user_input)
            print(response)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
