#!/usr/bin/env python3
"""
Fine-tuning Script for Python Coding Assistant
Compatible with Microsoft AI Toolkit for VS Code
"""

import json
import yaml
import os
import sys
from pathlib import Path

def load_config(config_path):
    """Load configuration from YAML file"""
    with open(config_path, 'r') as file:
        return yaml.safe_load(file)

def validate_dataset(dataset_path):
    """Validate the training dataset format"""
    print(f"🔍 Validating dataset: {dataset_path}")
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset file not found: {dataset_path}")
        return False
    
    try:
        with open(dataset_path, 'r') as file:
            lines = file.readlines()
            
        print(f"📊 Dataset contains {len(lines)} training examples")
        
        # Validate first few examples
        for i, line in enumerate(lines[:3]):
            try:
                data = json.loads(line.strip())
                required_fields = ['instruction', 'input', 'output']
                
                for field in required_fields:
                    if field not in data:
                        print(f"❌ Missing field '{field}' in example {i+1}")
                        return False
                        
                print(f"✅ Example {i+1}: Valid format")
                
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON in line {i+1}")
                return False
                
        print("✅ Dataset validation passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error validating dataset: {e}")
        return False

def prepare_for_ai_toolkit(config):
    """Prepare configuration for AI Toolkit"""
    print("🔧 Preparing configuration for AI Toolkit...")
    
    # Create AI Toolkit compatible configuration
    ai_toolkit_config = {
        "model_name": config['model_config']['base_model'],
        "dataset": {
            "path": config['training_config']['dataset_path'],
            "type": "instruction_following"
        },
        "training": {
            "epochs": config['training_config']['num_epochs'],
            "batch_size": config['training_config']['batch_size'],
            "learning_rate": config['training_config']['learning_rate'],
            "max_length": config['training_config']['max_seq_length']
        },
        "lora": {
            "enabled": config['training_config']['use_lora'],
            "rank": config['training_config']['lora_rank'],
            "alpha": config['training_config']['lora_alpha'],
            "dropout": config['training_config']['lora_dropout']
        },
        "output": {
            "directory": config['output_config']['output_dir'],
            "name": config['output_config']['model_name']
        }
    }
    
    # Save AI Toolkit configuration
    ai_toolkit_path = "./config/ai_toolkit_config.json"
    with open(ai_toolkit_path, 'w') as file:
        json.dump(ai_toolkit_config, file, indent=2)
    
    print(f"✅ AI Toolkit configuration saved to: {ai_toolkit_path}")
    return ai_toolkit_config

def show_training_info(config):
    """Display training information"""
    print("\n" + "="*60)
    print("🚀 FINE-TUNING CONFIGURATION")
    print("="*60)
    print(f"📦 Base Model: {config['model_config']['base_model']}")
    print(f"📊 Dataset: {config['training_config']['dataset_path']}")
    print(f"🔄 Epochs: {config['training_config']['num_epochs']}")
    print(f"📏 Batch Size: {config['training_config']['batch_size']}")
    print(f"🎯 Learning Rate: {config['training_config']['learning_rate']}")
    print(f"💾 Output: {config['output_config']['output_dir']}")
    print(f"🔧 LoRA Enabled: {config['training_config']['use_lora']}")
    print("="*60)

def create_ollama_modelfile(config):
    """Create Ollama Modelfile for the fine-tuned model"""
    modelfile_content = f"""# Modelfile for Python Coding Assistant
FROM {config['output_config']['output_dir']}/pytorch_model.bin

# Set parameters
PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER top_k 40

# Set the template
TEMPLATE \"\"\"
### Instruction:
{{{{ .Prompt }}}}

### Response:
\"\"\"

# System message
SYSTEM \"\"\"
{config['prompt_template']['system_prompt']}
\"\"\"
"""
    
    modelfile_path = "./models/Modelfile"
    os.makedirs(os.path.dirname(modelfile_path), exist_ok=True)
    
    with open(modelfile_path, 'w') as file:
        file.write(modelfile_content)
    
    print(f"✅ Ollama Modelfile created: {modelfile_path}")

def main():
    print("🤖 Python Coding Assistant Fine-tuning Script")
    print("=" * 50)
    
    # Load configuration
    config_path = "./config/fine_tune_config.yaml"
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        sys.exit(1)
    
    config = load_config(config_path)
    
    # Validate dataset
    if not validate_dataset(config['training_config']['dataset_path']):
        print("❌ Dataset validation failed!")
        sys.exit(1)
    
    # Show training information
    show_training_info(config)
    
    # Prepare for AI Toolkit
    ai_toolkit_config = prepare_for_ai_toolkit(config)
    
    # Create Ollama Modelfile
    create_ollama_modelfile(config)
    
    print("\n🎯 NEXT STEPS:")
    print("1. Open VS Code with AI Toolkit extension")
    print("2. Navigate to AI Toolkit sidebar")
    print("3. Select 'Fine-tune Model'")
    print("4. Load the configuration: ./config/ai_toolkit_config.json")
    print("5. Start the fine-tuning process")
    print("\n📚 OR use this script with AI Toolkit CLI:")
    print("   ai-toolkit fine-tune --config ./config/ai_toolkit_config.json")
    
    print("\n✅ Setup complete! Ready for fine-tuning.")

if __name__ == "__main__":
    main()
