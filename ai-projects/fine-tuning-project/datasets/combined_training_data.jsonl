{"instruction": "Write a Python function to add two numbers", "input": "", "output": "Here's a simple Python function to add two numbers:\n\n```python\ndef add_numbers(a, b):\n    \"\"\"\n    Add two numbers together.\n    \n    Args:\n        a (float): First number\n        b (float): Second number\n    \n    Returns:\n        float: Sum of a and b\n    \"\"\"\n    return a + b\n\n# Example usage:\nresult = add_numbers(5, 3)\nprint(f\"5 + 3 = {result}\")  # Output: 5 + 3 = 8\n```"}
{"instruction": "How do I create a list in Python?", "input": "", "output": "In Python, you can create lists in several ways:\n\n```python\n# 1. Empty list\nmy_list = []\n\n# 2. List with initial values\nfruits = ['apple', 'banana', 'orange']\nnumbers = [1, 2, 3, 4, 5]\n\n# 3. Using list() constructor\nanother_list = list()\n\n# 4. List with mixed data types\nmixed = ['hello', 42, True, 3.14]\n\n# 5. Using list comprehension\nsquares = [x**2 for x in range(5)]  # [0, 1, 4, 9, 16]\n\nprint(fruits)  # ['apple', 'banana', 'orange']\n```"}
{"instruction": "Write a Python function to check if a number is even or odd", "input": "", "output": "Here's a Python function to check if a number is even or odd:\n\n```python\ndef is_even_or_odd(number):\n    \"\"\"\n    Check if a number is even or odd.\n    \n    Args:\n        number (int): The number to check\n    \n    Returns:\n        str: 'even' if the number is even, 'odd' if odd\n    \"\"\"\n    if number % 2 == 0:\n        return 'even'\n    else:\n        return 'odd'\n\n# Example usage:\nprint(is_even_or_odd(4))   # Output: even\nprint(is_even_or_odd(7))   # Output: odd\nprint(is_even_or_odd(0))   # Output: even\n\n# Alternative version that returns boolean\ndef is_even(number):\n    return number % 2 == 0\n\nprint(is_even(4))  # True\nprint(is_even(7))  # False\n```"}
{"instruction": "How do I read a file in Python?", "input": "", "output": "Here are different ways to read files in Python:\n\n```python\n# Method 1: Using 'with' statement (recommended)\nwith open('filename.txt', 'r') as file:\n    content = file.read()\n    print(content)\n\n# Method 2: Read line by line\nwith open('filename.txt', 'r') as file:\n    for line in file:\n        print(line.strip())  # strip() removes newline characters\n\n# Method 3: Read all lines into a list\nwith open('filename.txt', 'r') as file:\n    lines = file.readlines()\n    print(lines)\n\n# Method 4: Read specific number of characters\nwith open('filename.txt', 'r') as file:\n    first_10_chars = file.read(10)\n    print(first_10_chars)\n\n# Always use 'with' statement - it automatically closes the file!\n```"}
{"instruction": "Write a Python function to find the maximum number in a list", "input": "", "output": "Here's a Python function to find the maximum number in a list:\n\n```python\ndef find_maximum(numbers):\n    \"\"\"\n    Find the maximum number in a list.\n    \n    Args:\n        numbers (list): List of numbers\n    \n    Returns:\n        float/int: Maximum number in the list\n    \"\"\"\n    if not numbers:  # Check if list is empty\n        return None\n    \n    return max(numbers)\n\n# Alternative manual implementation:\ndef find_max_manual(numbers):\n    if not numbers:\n        return None\n    \n    max_num = numbers[0]\n    for num in numbers:\n        if num > max_num:\n            max_num = num\n    return max_num\n\n# Example usage:\nnumbers = [3, 7, 2, 9, 1, 5]\nprint(find_maximum(numbers))     # Output: 9\nprint(find_max_manual(numbers))  # Output: 9\n```"}
{"instruction": "How do I create a for loop in Python?", "input": "", "output": "Here are different ways to create for loops in Python:\n\n```python\n# 1. Loop through a list\nfruits = ['apple', 'banana', 'orange']\nfor fruit in fruits:\n    print(fruit)\n\n# 2. Loop through a range of numbers\nfor i in range(5):  # 0, 1, 2, 3, 4\n    print(i)\n\n# 3. Loop with start and end\nfor i in range(2, 8):  # 2, 3, 4, 5, 6, 7\n    print(i)\n\n# 4. Loop with step\nfor i in range(0, 10, 2):  # 0, 2, 4, 6, 8\n    print(i)\n\n# 5. Loop with index and value\nfor index, fruit in enumerate(fruits):\n    print(f\"{index}: {fruit}\")\n\n# 6. Loop through a string\nfor char in \"hello\":\n    print(char)  # h, e, l, l, o\n```"}
{"instruction": "Write a Python function to calculate factorial", "input": "", "output": "Here's a Python function to calculate factorial:\n\n```python\ndef factorial(n):\n    \"\"\"\n    Calculate the factorial of a number.\n    \n    Args:\n        n (int): Non-negative integer\n    \n    Returns:\n        int: Factorial of n\n    \"\"\"\n    if n < 0:\n        raise ValueError(\"Factorial is not defined for negative numbers\")\n    if n == 0 or n == 1:\n        return 1\n    \n    result = 1\n    for i in range(2, n + 1):\n        result *= i\n    return result\n\n# Recursive version:\ndef factorial_recursive(n):\n    if n < 0:\n        raise ValueError(\"Factorial is not defined for negative numbers\")\n    if n == 0 or n == 1:\n        return 1\n    return n * factorial_recursive(n - 1)\n\n# Example usage:\nprint(factorial(5))           # Output: 120\nprint(factorial_recursive(5)) # Output: 120\nprint(factorial(0))           # Output: 1\n```"}
{"instruction": "How do I handle errors in Python?", "input": "", "output": "Here's how to handle errors in Python using try-except blocks:\n\n```python\n# Basic try-except\ntry:\n    number = int(input(\"Enter a number: \"))\n    result = 10 / number\n    print(f\"Result: {result}\")\nexcept ValueError:\n    print(\"Please enter a valid number!\")\nexcept ZeroDivisionError:\n    print(\"Cannot divide by zero!\")\n\n# Multiple exceptions in one block\ntry:\n    # Some risky code\n    pass\nexcept (ValueError, TypeError) as e:\n    print(f\"Error occurred: {e}\")\n\n# Catch all exceptions\ntry:\n    # Some code\n    pass\nexcept Exception as e:\n    print(f\"An error occurred: {e}\")\n\n# Try-except-else-finally\ntry:\n    result = 10 / 2\nexcept ZeroDivisionError:\n    print(\"Division by zero!\")\nelse:\n    print(\"No errors occurred\")\nfinally:\n    print(\"This always runs\")\n```"}
{"instruction": "Write a Python function to reverse a string", "input": "", "output": "Here are different ways to reverse a string in Python:\n\n```python\ndef reverse_string(text):\n    \"\"\"\n    Reverse a string.\n    \n    Args:\n        text (str): String to reverse\n    \n    Returns:\n        str: Reversed string\n    \"\"\"\n    return text[::-1]\n\n# Alternative methods:\ndef reverse_string_loop(text):\n    reversed_text = \"\"\n    for char in text:\n        reversed_text = char + reversed_text\n    return reversed_text\n\ndef reverse_string_builtin(text):\n    return ''.join(reversed(text))\n\n# Example usage:\noriginal = \"hello world\"\nprint(reverse_string(original))        # \"dlrow olleh\"\nprint(reverse_string_loop(original))   # \"dlrow olleh\"\nprint(reverse_string_builtin(original)) # \"dlrow olleh\"\n\n# Reverse words in a sentence\ndef reverse_words(sentence):\n    return ' '.join(sentence.split()[::-1])\n\nprint(reverse_words(\"hello world\"))  # \"world hello\"\n```"}
