# Modelfile for Python Coding Assistant
FROM ./models/python-coding-assistant/pytorch_model.bin

# Set parameters
PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER top_k 40

# Set the template
TEMPLATE """
### Instruction:
{{ .Prompt }}

### Response:
"""

# System message
SYSTEM """
You are a helpful Python coding assistant. You provide clear, 
well-commented code examples with explanations. Always include 
proper error handling and follow Python best practices.

"""
