# Fine-tuning Configuration for Python Coding Assistant
# This config works with AI Toolkit for VS Code

model_config:
  # Base model to fine-tune (using a small model for local training)
  base_model: "microsoft/Phi-3-mini-4k-instruct"
  model_type: "phi3"
  
  # Alternative models you can try:
  # base_model: "microsoft/Phi-3-mini-128k-instruct"
  # base_model: "mistralai/Mistral-7B-Instruct-v0.1"

training_config:
  # Dataset configuration
  dataset_path: "./datasets/combined_training_data.jsonl"
  dataset_format: "instruction"  # instruction-following format
  
  # Training parameters
  num_epochs: 3
  batch_size: 1
  learning_rate: 2e-5
  max_seq_length: 512
  
  # LoRA (Low-Rank Adaptation) parameters for efficient fine-tuning
  use_lora: true
  lora_rank: 16
  lora_alpha: 32
  lora_dropout: 0.1
  
  # Gradient settings
  gradient_accumulation_steps: 4
  warmup_steps: 100
  
  # Evaluation
  eval_steps: 50
  save_steps: 100
  logging_steps: 10

output_config:
  # Where to save the fine-tuned model
  output_dir: "./models/python-coding-assistant"
  save_total_limit: 2
  
  # Model naming
  model_name: "python-coding-assistant-v1"
  
hardware_config:
  # Local training settings
  device: "auto"  # Will use GPU if available, otherwise CPU
  fp16: true      # Use mixed precision for faster training
  
  # Memory optimization
  gradient_checkpointing: true
  dataloader_num_workers: 2

prompt_template:
  # Template for instruction-following format
  instruction_template: |
    ### Instruction:
    {instruction}
    
    ### Input:
    {input}
    
    ### Response:
    {output}
  
  # System prompt for the fine-tuned model
  system_prompt: |
    You are a helpful Python coding assistant. You provide clear, 
    well-commented code examples with explanations. Always include 
    proper error handling and follow Python best practices.

validation_config:
  # Validation dataset (optional)
  validation_split: 0.1
  
  # Metrics to track
  metrics:
    - "loss"
    - "perplexity"
    - "accuracy"
