# 🎯 AI Toolkit Fine-tuning Guide

## Complete Guide to Fine-tuning Models with Microsoft AI Toolkit

### 🎉 What You've Built

You now have a complete fine-tuning setup for creating a **Python Coding Assistant**:

- ✅ **Training Dataset** - 9 high-quality Python coding examples
- ✅ **Configuration Files** - Ready for AI Toolkit
- ✅ **Validation Scripts** - Automated dataset checking
- ✅ **Ollama Integration** - Deploy fine-tuned models locally

---

## 🚀 Method 1: Using AI Toolkit GUI (Recommended for Beginners)

### Step 1: Open AI Toolkit in VS Code
1. **Open VS Code Insiders** with your project
2. **Click the AI Toolkit icon** in the sidebar (🤖)
3. **Navigate to "Fine-tune"** section

### Step 2: Start Fine-tuning Process
1. **Click "Create New Fine-tune Project"**
2. **Select Model**: Choose `microsoft/Phi-3-mini-4k-instruct`
3. **Upload Dataset**: Select `./datasets/combined_training_data.jsonl`
4. **Configure Parameters**:
   - Epochs: `3`
   - Batch Size: `1`
   - Learning Rate: `2e-5`
   - Enable LoRA: `Yes`

### Step 3: Monitor Training
- **Watch the progress bar** and loss metrics
- **Training time**: ~30-60 minutes (depending on hardware)
- **Check logs** for any errors

---

## 🔧 Method 2: Using Configuration Files

### Load Pre-configured Settings
1. In AI Toolkit, click **"Import Configuration"**
2. Select: `./config/ai_toolkit_config.json`
3. Review settings and click **"Start Fine-tuning"**

---

## 💻 Method 3: Command Line (Advanced)

### Install Dependencies
```bash
pip install -r requirements.txt
```

### Run Fine-tuning Script
```bash
# Validate setup
python3 scripts/fine_tune_model.py

# If AI Toolkit CLI is available:
ai-toolkit fine-tune --config ./config/ai_toolkit_config.json
```

---

## 📊 Understanding Your Dataset

### Dataset Format (JSONL)
Each line contains:
```json
{
  "instruction": "Write a Python function to add two numbers",
  "input": "",
  "output": "Here's a simple Python function..."
}
```

### Current Training Examples:
1. **Basic Functions** - add numbers, check even/odd
2. **Data Structures** - lists, loops, file handling
3. **Error Handling** - try-except blocks
4. **String Operations** - reversing, manipulation
5. **Mathematical Functions** - factorial, maximum

---

## ⚙️ Fine-tuning Parameters Explained

### **LoRA (Low-Rank Adaptation)**
- **Enabled**: `True` ✅
- **Rank**: `16` (balance between quality and speed)
- **Alpha**: `32` (learning rate scaling)
- **Benefits**: Faster training, less memory usage

### **Training Settings**
- **Epochs**: `3` (how many times to see the data)
- **Batch Size**: `1` (conservative for local training)
- **Learning Rate**: `2e-5` (small steps for stable learning)

---

## 🎯 Expected Results

### After Fine-tuning:
- **Specialized Python Assistant** - Better at coding tasks
- **Consistent Code Style** - Follows your examples
- **Improved Explanations** - More detailed responses
- **Domain Knowledge** - Focused on Python best practices

### Performance Improvements:
- **Before**: Generic responses
- **After**: Python-specific, well-commented code with examples

---

## 🔄 Testing Your Fine-tuned Model

### Method 1: AI Toolkit Playground
1. **Load your fine-tuned model** in AI Toolkit
2. **Test with prompts** like:
   - "Write a function to sort a list"
   - "How do I handle file exceptions?"
   - "Create a class for a simple calculator"

### Method 2: Ollama Integration
```bash
# Create Ollama model from fine-tuned weights
ollama create python-assistant -f ./models/Modelfile

# Test the model
ollama run python-assistant "Write a Python function to find prime numbers"
```

---

## 📈 Next Steps & Advanced Features

### 1. **Expand Your Dataset**
- Add more Python examples (aim for 100+ examples)
- Include different difficulty levels
- Add specific domains (web dev, data science, etc.)

### 2. **Experiment with Models**
- Try `microsoft/Phi-3-mini-128k-instruct` for longer context
- Test `mistralai/Mistral-7B-Instruct-v0.1` for more capability

### 3. **Advanced Techniques**
- **Multi-turn conversations** - Add dialogue examples
- **Code review** - Train on code improvement tasks
- **Documentation** - Add docstring generation examples

### 4. **Evaluation & Metrics**
- Create test prompts to measure improvement
- Compare responses before/after fine-tuning
- Use automated code quality metrics

---

## 🛠️ Troubleshooting

### Common Issues:
1. **Out of Memory**: Reduce batch size to 1, enable gradient checkpointing
2. **Slow Training**: Use GPU if available, reduce sequence length
3. **Poor Results**: Add more diverse training examples
4. **Model Not Loading**: Check file paths and permissions

### Hardware Requirements:
- **Minimum**: 8GB RAM, CPU training
- **Recommended**: 16GB RAM, GPU with 6GB+ VRAM
- **Optimal**: 32GB RAM, GPU with 12GB+ VRAM

---

## 🎓 Congratulations!

You've successfully learned:
- ✅ **Dataset preparation** for fine-tuning
- ✅ **Configuration management** for AI Toolkit
- ✅ **Multiple training methods** (GUI, CLI, scripts)
- ✅ **Model deployment** with Ollama
- ✅ **Performance optimization** with LoRA

**You're now ready to create specialized AI models for any domain!** 🚀
